<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<!-- Dashboard Content -->
<div class="container-fluid">
    <!-- Welcome Section -->
    <div class="card mb-4 border-start border-navy border-4">
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col">
                    <h2 class="h4 fw-bold text-gray-800 mb-1">Welcome back, <?= $user_name ?>!</h2>
                    <p class="text-muted mb-0">Manage your recruitment activities and job postings.</p>
                </div>
                <div class="col-auto">
                    <span class="text-muted"><?= date('l, F j, Y') ?></span>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Grid -->
    <div class="row g-4 mb-4">
        <!-- Active Jobs Card -->
        <div class="col-12 col-sm-6 col-lg-3">
            <div class="card hover-card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <p class="text-muted small mb-2">Active Jobs</p>
                            <h3 class="fw-bold mb-3"><?= number_format($active_jobs) ?></h3>
                        </div>
                        <div class="bg-navy bg-opacity-10 rounded p-3">
                            <i class="fas fa-briefcase text-navy fs-4"></i>
                        </div>
                    </div>
                    <div class="d-flex align-items-center">
                        <span class="text-success me-2">
                            <i class="fas fa-arrow-up me-1"></i><?= $new_jobs_this_week ?>
                        </span>
                        <span class="text-muted small">New this week</span>
                    </div>
                </div>
                <div class="card-footer p-0">
                    <div class="bg-gradient" style="height: 4px; background: linear-gradient(to right, var(--navy), #003B7B);"></div>
                </div>
            </div>
        </div>

        <!-- Total Applications Card -->
        <div class="col-12 col-sm-6 col-lg-3">
            <div class="card hover-card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <p class="text-muted small mb-2">Total Applications</p>
                            <h3 class="fw-bold mb-3"><?= number_format($total_applications) ?></h3>
                        </div>
                        <div class="bg-accent-red bg-opacity-10 rounded p-3">
                            <i class="fas fa-file-alt text-accent-red fs-4"></i>
                        </div>
                    </div>
                    <div class="d-flex align-items-center">
                        <span class="text-success me-2">
                            <i class="fas fa-arrow-up me-1"></i><?= $new_applications_today ?>
                        </span>
                        <span class="text-muted small">New today</span>
                    </div>
                </div>
                <div class="card-footer p-0">
                    <div class="bg-gradient" style="height: 4px; background: linear-gradient(to right, var(--accent-red), var(--deep-red));"></div>
                </div>
            </div>
        </div>

        <!-- Shortlisted Candidates -->
        <div class="col-12 col-sm-6 col-lg-3">
            <div class="card hover-card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <p class="text-muted small mb-2">Shortlisted</p>
                            <h3 class="fw-bold mb-3"><?= number_format($shortlisted_count) ?></h3>
                        </div>
                        <div class="bg-lime bg-opacity-10 rounded p-3">
                            <i class="fas fa-user-check text-dark-green fs-4"></i>
                        </div>
                    </div>
                    <div class="d-flex align-items-center">
                        <span class="text-success me-2">
                            <i class="fas fa-arrow-up me-1"></i><?= $new_shortlisted_this_week ?>
                        </span>
                        <span class="text-muted small">This week</span>
                    </div>
                </div>
                <div class="card-footer p-0">
                    <div class="bg-gradient" style="height: 4px; background: linear-gradient(to right, var(--lime), var(--dark-green));"></div>
                </div>
            </div>
        </div>

        <!-- Interviews Scheduled -->
        <div class="col-12 col-sm-6 col-lg-3">
            <div class="card hover-card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <p class="text-muted small mb-2">Interviews</p>
                            <h3 class="fw-bold mb-3"><?= number_format($interviews_count) ?></h3>
                        </div>
                        <div class="bg-info bg-opacity-10 rounded p-3">
                            <i class="fas fa-calendar-check text-info fs-4"></i>
                        </div>
                    </div>
                    <div class="d-flex align-items-center">
                        <span class="text-muted small">Scheduled this week</span>
                    </div>
                </div>
                <div class="card-footer p-0">
                    <div class="bg-gradient" style="height: 4px; background: linear-gradient(to right, #0dcaf0, #0d6efd);"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Access Section -->
    <div class="card mb-4">
        <div class="card-body">
            <h5 class="card-title mb-4">Quick Access</h5>
            <div class="row g-4">
                <!-- Manage Exercises -->
                <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                    <a href="<?= base_url('exercises') ?>" class="card text-decoration-none hover-card">
                        <div class="card-body d-flex align-items-center">
                            <i class="fas fa-tasks text-accent-red fs-4 me-3"></i>
                            <span class="text-dark fw-medium">Manage Exercises</span>
                        </div>
                    </a>
                </div>

                <!-- Manage Position Groups -->
                <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                    <a href="<?= base_url('positions/positions_exercises') ?>" class="card text-decoration-none hover-card">
                        <div class="card-body d-flex align-items-center">
                            <i class="fas fa-briefcase text-accent-red fs-4 me-3"></i>
                            <span class="text-dark fw-medium">Manage Positions</span>
                        </div>
                    </a>
                </div>

                <!-- Incoming Applications -->
                <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                    <a href="<?= base_url('incoming_applications') ?>" class="card text-decoration-none hover-card">
                        <div class="card-body d-flex align-items-center justify-content-between">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-file-alt text-accent-red fs-4 me-3"></i>
                                <span class="text-dark fw-medium">Incoming Applications</span>
                            </div>
                            <?php if (isset($unacknowledged_applications_count) && $unacknowledged_applications_count > 0): ?>
                                <span class="badge bg-danger rounded-pill"><?= $unacknowledged_applications_count ?></span>
                            <?php endif; ?>
                        </div>
                    </a>
                </div>

                <!-- Acknowledged Applications -->
                <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                    <a href="<?= base_url('acknowledged_applications') ?>" class="card text-decoration-none hover-card">
                        <div class="card-body d-flex align-items-center">
                            <i class="fas fa-check-circle text-accent-red fs-4 me-3"></i>
                            <span class="text-dark fw-medium">Acknowledged Applications</span>
                        </div>
                    </a>
                </div>

                 <!-- Applications Pre-Screening -->
                 <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                    <a href="<?= base_url('application_pre_screening') ?>" class="card text-decoration-none hover-card">
                        <div class="card-body d-flex align-items-center">
                            <i class="fas fa-clipboard-check text-accent-red fs-4 me-3"></i>
                            <span class="text-dark fw-medium">Pre-Screening</span>
                        </div>
                    </a>
                </div>

                <!-- Applications Profiling -->
                <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                    <a href="<?= base_url('profile_applications_exercise') ?>" class="card text-decoration-none hover-card">
                        <div class="card-body d-flex align-items-center">
                            <i class="fas fa-user-tag text-accent-red fs-4 me-3"></i>
                            <span class="text-dark fw-medium">Profiling</span>
                        </div>
                    </a>
                </div>

                <!-- Scoring/Rating -->
                <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                    <a href="<?= base_url('rating') ?>" class="card text-decoration-none hover-card">
                        <div class="card-body d-flex align-items-center">
                            <i class="fas fa-star text-accent-red fs-4 me-3"></i>
                            <span class="text-dark fw-medium">Scoring/Rating</span>
                        </div>
                    </a>
                </div>

                <!-- Shortlist (Shortlist/Elimination) -->
                <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                    <a href="<?= base_url('shortlisting') ?>" class="card text-decoration-none hover-card">
                        <div class="card-body d-flex align-items-center">
                            <i class="fas fa-list-check text-accent-red fs-4 me-3"></i>
                            <span class="text-dark fw-medium">Shortlist (Shortlist/Elimination)</span>
                        </div>
                    </a>
                </div>

                <!-- Interviews -->
                <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                    <a href="<?= base_url('interviews') ?>" class="card text-decoration-none hover-card">
                        <div class="card-body d-flex align-items-center">
                            <i class="fas fa-calendar-alt text-accent-red fs-4 me-3"></i>
                            <span class="text-dark fw-medium">Interviews</span>
                        </div>
                    </a>
                </div>

                <!-- Reports -->
                <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                    <a href="<?= base_url('reports') ?>" class="card text-decoration-none hover-card">
                        <div class="card-body d-flex align-items-center">
                            <i class="fas fa-chart-bar text-accent-red fs-4 me-3"></i>
                            <span class="text-dark fw-medium">Reports</span>
                        </div>
                    </a>
                </div>

                <!-- Settings -->
                <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                    <a href="<?= base_url('settings') ?>" class="card text-decoration-none hover-card">
                        <div class="card-body d-flex align-items-center">
                            <i class="fas fa-cog text-accent-red fs-4 me-3"></i>
                            <span class="text-dark fw-medium">Settings</span>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Exercises -->
    <div class="card mb-4">
        <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Recent Exercises</h5>
            <a href="<?= base_url('exercises') ?>" class="btn btn-link text-accent-red text-decoration-none">View All</a>
        </div>
        <div class="card-body">
            <?php if (!empty($recent_exercises)): ?>
                <div class="row g-3">
                    <?php foreach ($recent_exercises as $exercise): ?>
                        <div class="col-md-4">
                            <div class="card border-start border-accent-red border-3 h-100">
                                <div class="card-body">
                                    <h6 class="card-title text-truncate"><?= esc($exercise['exercise_name']) ?></h6>
                                    <p class="card-text small text-muted mb-2">
                                        <i class="fas fa-calendar me-1"></i>
                                        <?= date('M j, Y', strtotime($exercise['created_at'])) ?>
                                    </p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="badge bg-<?= $exercise['status'] === 'published' ? 'success' : ($exercise['status'] === 'draft' ? 'warning' : 'secondary') ?> bg-opacity-10 text-<?= $exercise['status'] === 'published' ? 'success' : ($exercise['status'] === 'draft' ? 'warning' : 'secondary') ?>">
                                            <?= ucfirst($exercise['status']) ?>
                                        </span>
                                        <a href="<?= base_url('exercises/view/' . $exercise['id']) ?>" class="btn btn-sm btn-outline-primary">View</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="text-center py-4 text-muted">
                    <i class="fas fa-tasks fa-2x mb-2 d-block"></i>
                    No recent exercises found
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Recent Applications -->
    <div class="card mb-4">
        <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Recent Applications</h5>
            <a href="<?= base_url('incoming_applications') ?>" class="btn btn-link text-accent-red text-decoration-none">View All</a>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover align-middle mb-0">
                    <thead class="table-light">
                        <tr>
                            <th class="px-4">Applicant</th>
                            <th>Position</th>
                            <th>Status</th>
                            <th>Applied Date</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (!empty($recent_applications)): ?>
                            <?php foreach ($recent_applications as $application): ?>
                                <tr>
                                    <td class="px-4">
                                        <div class="d-flex align-items-center">
                                            <div class="bg-light rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 32px; height: 32px;">
                                                <i class="fas fa-user text-muted"></i>
                                            </div>
                                            <span class="fw-medium"><?= esc($application['first_name']) ?> <?= esc($application['last_name']) ?></span>
                                        </div>
                                    </td>
                                    <td><?= esc($application['designation'] ?? 'N/A') ?></td>
                                    <td>
                                        <?php
                                        $status = $application['application_status'] ?? 'received';
                                        $badgeClass = 'bg-secondary';
                                        $statusText = ucfirst(str_replace('_', ' ', $status));

                                        switch ($status) {
                                            case 'received':
                                            case 'pending_prescreen':
                                                $badgeClass = 'bg-warning';
                                                break;
                                            case 'shortlisted':
                                                $badgeClass = 'bg-success';
                                                break;
                                            case 'rejected':
                                                $badgeClass = 'bg-danger';
                                                break;
                                            case 'interview_scheduled':
                                            case 'interviewed':
                                                $badgeClass = 'bg-info';
                                                break;
                                        }
                                        ?>
                                        <span class="badge <?= $badgeClass ?> bg-opacity-10 text-<?= str_replace('bg-', '', $badgeClass) ?>"><?= $statusText ?></span>
                                    </td>
                                    <td><?= date('M j, Y', strtotime($application['created_at'])) ?></td>
                                    <td>
                                        <a href="<?= base_url('incoming_applications/view/' . $application['id']) ?>" class="btn btn-sm btn-outline-primary">View</a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="5" class="text-center py-4 text-muted">
                                    <i class="fas fa-inbox fa-2x mb-2 d-block"></i>
                                    No recent applications found
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Upcoming Interviews -->
    <div class="card mb-4">
        <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Upcoming Interviews</h5>
            <a href="<?= base_url('interviews') ?>" class="btn btn-link text-info text-decoration-none">View All</a>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover align-middle mb-0">
                    <thead class="table-light">
                        <tr>
                            <th class="px-4">Candidate</th>
                            <th>Position</th>
                            <th>Date & Time</th>
                            <th>Type</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (!empty($upcoming_interviews)): ?>
                            <?php foreach ($upcoming_interviews as $interview): ?>
                                <tr>
                                    <td class="px-4">
                                        <div class="d-flex align-items-center">
                                            <div class="bg-light rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 32px; height: 32px;">
                                                <i class="fas fa-user text-muted"></i>
                                            </div>
                                            <span class="fw-medium"><?= esc($interview['candidate_name']) ?></span>
                                        </div>
                                    </td>
                                    <td><?= esc($interview['position']) ?></td>
                                    <td><?= date('M j, Y g:i A', strtotime($interview['interview_date'])) ?></td>
                                    <td>
                                        <span class="badge bg-info bg-opacity-10 text-info"><?= esc($interview['interview_type']) ?></span>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-success">
                                            <?= $interview['interview_type'] === 'Virtual' ? 'Join' : 'View' ?>
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="5" class="text-center py-4 text-muted">
                                    <i class="fas fa-calendar-times fa-2x mb-2 d-block"></i>
                                    No upcoming interviews scheduled
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('Dashboard scripts initialized');

    // Find and report any JavaScript syntax errors
    try {
        // Check for common syntax issues
        const scripts = document.querySelectorAll('script');
        scripts.forEach((script, index) => {
            if (script.innerHTML.trim()) {
                try {
                    // Try to evaluate the script content to check for syntax errors
                    new Function(script.innerHTML);
                } catch (e) {
                    console.error(`Syntax error in script #${index}:`, e.message);
                    console.log('Script content:', script.innerHTML.substring(0, 100) + '...');
                }
            }
        });
    } catch (e) {
        console.error('Error in error detection script:', e);
    }
});
</script>
<?= $this->endSection() ?>